@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-poppins);
  --font-mono: var(--font-poppins);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --font-poppins: "Poppins", system-ui, sans-serif;
  /* Modern Light Theme with #55a4ff as primary */
  --background: oklch(0.99 0.005 240);
  --foreground: oklch(0.15 0.02 240);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.02 240);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.02 240);
  --primary: oklch(0.7 0.15 240); /* #55a4ff converted to OKLCH */
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.96 0.01 240);
  --secondary-foreground: oklch(0.15 0.02 240);
  --muted: oklch(0.96 0.01 240);
  --muted-foreground: oklch(0.5 0.02 240);
  --accent: oklch(0.94 0.02 240);
  --accent-foreground: oklch(0.15 0.02 240);
  --destructive: oklch(0.65 0.2 25);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.9 0.01 240);
  --input: oklch(0.96 0.01 240);
  --ring: oklch(0.7 0.15 240);
  --chart-1: oklch(0.7 0.15 240); /* Primary blue */
  --chart-2: oklch(0.65 0.12 180); /* Complementary teal */
  --chart-3: oklch(0.7 0.15 240); /* Main color #55a4ff */
  --chart-4: oklch(0.75 0.1 60); /* Warm accent */
  --chart-5: oklch(0.6 0.15 300); /* Purple accent */
  --sidebar: oklch(1 0 0);
  --sidebar-foreground: oklch(0.15 0.02 240);
  --sidebar-primary: oklch(0.7 0.15 240);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.94 0.02 240);
  --sidebar-accent-foreground: oklch(0.15 0.02 240);
  --sidebar-border: oklch(0.9 0.01 240);
  --sidebar-ring: oklch(0.7 0.15 240);
}

.dark {
  /* Modern Dark Theme with #55a4ff as primary */
  --background: oklch(0.12 0.02 240);
  --foreground: oklch(0.95 0.01 240);
  --card: oklch(0.15 0.02 240);
  --card-foreground: oklch(0.95 0.01 240);
  --popover: oklch(0.15 0.02 240);
  --popover-foreground: oklch(0.95 0.01 240);
  --primary: oklch(0.75 0.15 240); /* Slightly brighter #55a4ff for dark mode */
  --primary-foreground: oklch(0.1 0.02 240);
  --secondary: oklch(0.2 0.02 240);
  --secondary-foreground: oklch(0.95 0.01 240);
  --muted: oklch(0.2 0.02 240);
  --muted-foreground: oklch(0.6 0.02 240);
  --accent: oklch(0.25 0.03 240);
  --accent-foreground: oklch(0.95 0.01 240);
  --destructive: oklch(0.7 0.2 25);
  --destructive-foreground: oklch(0.95 0.01 240);
  --border: oklch(0.25 0.02 240);
  --input: oklch(0.2 0.02 240);
  --ring: oklch(0.75 0.15 240);
  --chart-1: oklch(0.75 0.15 240); /* Primary blue */
  --chart-2: oklch(0.7 0.12 180); /* Complementary teal */
  --chart-3: oklch(0.75 0.15 240); /* Main color #55a4ff */
  --chart-4: oklch(0.8 0.1 60); /* Warm accent */
  --chart-5: oklch(0.65 0.15 300); /* Purple accent */
  --sidebar: oklch(0.15 0.02 240);
  --sidebar-foreground: oklch(0.95 0.01 240);
  --sidebar-primary: oklch(0.75 0.15 240);
  --sidebar-primary-foreground: oklch(0.1 0.02 240);
  --sidebar-accent: oklch(0.25 0.03 240);
  --sidebar-accent-foreground: oklch(0.95 0.01 240);
  --sidebar-border: oklch(0.25 0.02 240);
  --sidebar-ring: oklch(0.75 0.15 240);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-poppins);
  }
}
