"use client";

import { useState } from "react";
import <PERSON><PERSON><PERSON><PERSON> from "pizzip";
import Docxtemplater from "docxtemplater";
import mammoth from "mammoth";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import ImageModule from "docxtemplater-image-module-free";

export default function GoodMoralPage() {
  const [form, setForm] = useState({
    name: "",
    date: "",
    address: "",
    image: "", // base64 image data
  });

  const [previewHTML, setPreviewHTML] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);

  const handleTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = () => {
      const img = new Image();
      img.src = reader.result as string;

      img.onload = () => {
        const canvas = document.createElement("canvas");
        const size = Math.min(img.width, img.height); // Crop to square
        const sx = (img.width - size) / 2;
        const sy = (img.height - size) / 2;

        canvas.width = 100;
        canvas.height = 100;

        const ctx = canvas.getContext("2d");
        if (ctx) {
          ctx.drawImage(img, sx, sy, size, size, 0, 0, 100, 100);
          const base64 = canvas.toDataURL("image/jpeg").split(",")[1];
          setForm((prev) => ({ ...prev, image: base64 }));
        }
      };
    };
    reader.readAsDataURL(file);
  };

  const generatePDFFromHTML = async (htmlContent: string, filename: string) => {
    const cleanHTML = `
      <div style="font-family: Arial, sans-serif; font-size: 14px; line-height: 1.6; color: #000000; background-color: #ffffff; padding: 40px; max-width: 800px; margin: 0 auto;">
        ${htmlContent
          .replace(/<style[^>]*>.*?<\/style>/gi, "")
          .replace(/style="[^"]*"/gi, "")}
      </div>
    `;

    const iframe = document.createElement("iframe");
    iframe.style.position = "absolute";
    iframe.style.left = "-9999px";
    iframe.style.width = "210mm";
    iframe.style.height = "297mm";
    document.body.appendChild(iframe);

    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!iframeDoc) throw new Error("Could not access iframe document");

    iframeDoc.open();
    iframeDoc.write(`
      <!DOCTYPE html>
      <html><head><meta charset="utf-8"></head><body>${cleanHTML}</body></html>
    `);
    iframeDoc.close();

    await new Promise((resolve) => {
      iframe.onload = resolve;
      setTimeout(resolve, 100);
    });

    const canvas = await html2canvas(iframeDoc.body, {
      scale: 2,
      useCORS: true,
      backgroundColor: "#ffffff",
      width: 794,
      height: 1123,
    });

    document.body.removeChild(iframe);

    const pdf = new jsPDF("p", "mm", "a4");
    const imgData = canvas.toDataURL("image/png");
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();
    const ratio = Math.min(pdfWidth / canvas.width, pdfHeight / canvas.height);
    const imgX = (pdfWidth - canvas.width * ratio) / 2;
    const imgY = 0;

    pdf.addImage(
      imgData,
      "PNG",
      imgX,
      imgY,
      canvas.width * ratio,
      canvas.height * ratio
    );
    pdf.save(filename);
  };

  const generateDocument = async () => {
    if (!form.name || !form.date || !form.address) {
      alert("Please fill in all required fields");
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch("/example.docx");
      if (!response.ok) throw new Error("Failed to load template");

      const content = await response.arrayBuffer();
      const zip = new PizZip(content);

      const modules = [];
      if (form.image) {
        const imageOpts = {
          centered: false,
          getImage: (tagValue: string) =>
            Uint8Array.from(atob(tagValue), (c) => c.charCodeAt(0)),
          getSize: (): [number, number] => [100, 100], // Size in pixels (1x1 square)
        };
        modules.push(new ImageModule(imageOpts));
      }

      const doc = new Docxtemplater(zip, {
        modules,
        paragraphLoop: true,
        linebreaks: true,
      });

      const templateData: any = {
        name: form.name,
        date: form.date,
        address: form.address,
      };

      if (form.image) templateData.image = form.image;

      doc.setData(templateData);
      doc.render();

      const outputBlob = doc.getZip().generate({ type: "blob" });
      const { value } = await mammoth.convertToHtml({
        arrayBuffer: await outputBlob.arrayBuffer(),
      });

      setPreviewHTML(value);
      await generatePDFFromHTML(value, "good-moral-certificate.pdf");
    } catch (err) {
      console.error("Error generating document:", err);
      alert("Something went wrong. Try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">
        Good Moral Certificate Generator
      </h1>

      <div className="max-w-md mx-auto">
        <div className="space-y-4">
          <h2 className="text-lg font-semibold mb-4">Fill in the details</h2>

          <div className="flex justify-center mb-6">
            <div>
              <input
                id="image"
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
              <div
                onClick={() => document.getElementById("image")?.click()}
                className="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-gray-400 transition-colors"
              >
                {form.image ? (
                  <img
                    src={`data:image/jpeg;base64,${form.image}`}
                    alt="Preview"
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <div className="text-center text-gray-500">
                    <svg
                      className="w-8 h-8 mx-auto mb-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                    <p className="text-sm">Click to upload</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div>
            <label htmlFor="name" className="block text-sm font-medium mb-2">
              Name *
            </label>
            <input
              id="name"
              type="text"
              name="name"
              placeholder="Enter full name"
              value={form.name}
              onChange={handleTextChange}
              className="w-full border px-3 py-2 rounded-md"
              required
            />
          </div>

          <div>
            <label htmlFor="date" className="block text-sm font-medium mb-2">
              Date *
            </label>
            <input
              id="date"
              type="date"
              name="date"
              value={form.date}
              onChange={handleTextChange}
              className="w-full border px-3 py-2 rounded-md"
              required
            />
          </div>

          <div>
            <label htmlFor="address" className="block text-sm font-medium mb-2">
              Place *
            </label>
            <input
              id="address"
              type="text"
              name="address"
              placeholder="Enter place/address"
              value={form.address}
              onChange={handleTextChange}
              className="w-full border px-3 py-2 rounded-md"
              required
            />
          </div>

          <button
            onClick={generateDocument}
            disabled={isLoading}
            className="w-full bg-chart-3 hover:bg-chart-3/90 text-foreground font-medium py-2 px-4 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? "Generating..." : "Generate & Download PDF"}
          </button>
        </div>
      </div>
    </div>
  );
}
